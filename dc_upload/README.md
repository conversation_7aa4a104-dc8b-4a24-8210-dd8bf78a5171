# DC文件上传工具

这是一个用于批量上传题目到DC系统的Python工具。

## 功能特性

1. **批量处理**: 支持批量处理文件夹中的所有JSON文件
2. **并行处理**: 使用pqdm库实现多进程并行上传，大幅提升处理效率
3. **智能来源选择**: 当指定的来源没有找到时，自动选择第一个可用选项
4. **详细日志**: 提供详细的处理过程日志，便于跟踪上传状态
5. **错误处理**: 完善的错误处理机制，单个文件失败不影响其他文件处理
6. **进度显示**: 实时显示处理进度和统计信息

## 使用方法

### 1. 批量处理文件夹

```bash
python dc_uploader.py <文件夹路径> [并行数量]
```

例如：
```bash
# 使用默认并行数量(3)
python dc_uploader.py /path/to/json/files

# 指定并行数量为5
python dc_uploader.py /path/to/json/files 5
```

### 2. 单文件测试模式

```bash
python dc_uploader.py
```

不提供参数时，会使用默认的单文件测试模式。

## JSON文件格式

JSON文件应包含以下字段：

```json
{
  "编号": "题目编号",
  "学科": "数学",
  "题型": "单选题",
  "科目": "数二",
  "来源": ["线性代数-日练习", "行列式", "基础"],
  "年份": 2024,
  "题干": "题目内容",
  "选项": [
    {"choice": "A", "context": "选项A的内容"},
    {"choice": "B", "context": "选项B的内容"},
    {"choice": "C", "context": "选项C的内容"},
    {"choice": "D", "context": "选项D的内容"}
  ],
  "答案": "A",
  "分析点评": "分析点评内容",
  "文字解答": "文字解答内容"
}
```

## 重要说明

1. **认证令牌**: 使用前需要更新代码中的JWT令牌
2. **来源选择**: 如果指定的来源路径没有找到，系统会自动选择第一个可用选项
3. **浏览器要求**: 需要安装Chrome浏览器，工具会自动启动浏览器进行操作
4. **网络要求**: 需要能够访问DC系统的网络环境
5. **并行数量**: 建议并行数量设置为2-5，过高可能导致系统资源不足
6. **资源消耗**: 每个并行任务都会启动一个浏览器实例，请根据系统性能调整并行数量

## 处理结果

批量处理完成后，会显示处理统计：
- 成功上传的文件数量
- 失败的文件数量
- 总文件数量

每个文件的处理结果都会实时显示，便于跟踪进度。

## 错误处理

- JSON格式错误：会跳过该文件并继续处理其他文件
- 网络错误：会记录错误信息并继续处理
- 文件读取错误：会记录错误信息并继续处理

## 依赖要求

- Python 3.7+
- playwright
- requests
- pqdm (并行处理)
- pathlib (Python标准库)

安装依赖：
```bash
pip install playwright requests pqdm
playwright install chromium
```

## 性能优化

### 并行处理优势
- **提升效率**: 并行处理可以显著减少总处理时间
- **资源利用**: 充分利用多核CPU和网络带宽
- **可配置**: 可根据系统性能调整并行数量

### 建议的并行数量
- **低配置系统**: 2-3个并行任务
- **中等配置系统**: 3-5个并行任务
- **高配置系统**: 5-8个并行任务
- **最大限制**: 系统限制最大10个并行任务

### 性能监控
使用测试脚本可以测试不同并行数量的性能：
```bash
python test_parallel_upload.py 3
```
